import { request } from '@/utils/request.js'

/**
 * 验证优惠码
 * @param {Object} data - 优惠码验证数据
 * @param {string} data.couponCode - 优惠码
 * @param {number} data.orderAmount - 订单金额
 * @param {string} data.orderNo - 订单号
 */
export function validateCoupon(data) {
  return request({
    url: '/api/coupon/validate',
    method: 'POST',
    data: data
  })
}

/**
 * 获取用户可用优惠码列表
 * @param {Object} params - 查询参数
 * @param {number} params.orderAmount - 订单金额
 */
export function getAvailableCoupons(params) {
  return request({
    url: '/api/coupon/available',
    method: 'GET',
    params: params
  })
}

/**
 * 应用优惠码到订单
 * @param {Object} data - 应用优惠码数据
 * @param {string} data.couponCode - 优惠码
 * @param {string} data.orderNo - 订单号
 */
export function applyCoupon(data) {
  return request({
    url: '/api/coupon/apply',
    method: 'POST',
    data: data
  })
}

/**
 * 取消应用的优惠码
 * @param {Object} data - 取消优惠码数据
 * @param {string} data.orderNo - 订单号
 */
export function cancelCoupon(data) {
  return request({
    url: '/api/coupon/cancel',
    method: 'POST',
    data: data
  })
}
